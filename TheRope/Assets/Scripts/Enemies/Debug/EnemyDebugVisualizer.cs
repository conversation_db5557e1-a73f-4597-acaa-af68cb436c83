// Copyright Isto Inc.

using TMPro;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    /// <summary>
    /// Component responsible for visualizing enemy debug information.
    /// <PERSON>les debug billboard, detection rays, and other visual debugging aids.
    /// </summary>
    public class EnemyDebugVisualizer : MonoBehaviour
    {
        // UNITY HOOKUP

        [Header("Debug Settings")]
        [SerializeField] private bool _isDebugging = false;
        [SerializeField] private Canvas _debugBillboard;
        [SerializeField] private TextMeshProUGUI _debugText;

        [Header("Detection Visualization")]
        [SerializeField] private bool _showDetectionRadius = true;
        [SerializeField] private Color _detectionRayColor = Color.red;
        [SerializeField] private int _detectionRayCount = 36;


        // PRIVATE FIELDS

        private static readonly float CAMERA_SEARCH_INTERVAL = 1f;
        private Transform _localPlayerCamera;
        private float _lastCameraSearchTime;
        private float _detectionRadius;


        // PROPERTIES

        /// <summary>
        /// True if debugging is enabled
        /// </summary>
        public bool IsDebugging => _isDebugging;


        // LIFECYCLE METHODS

        private void Awake()
        {
            if (_debugBillboard != null)
            {
                _debugBillboard.gameObject.SetActive(_isDebugging);
            }
        }

        private void Update()
        {
            if (!_isDebugging)
            {
                return;
            }

            if (_showDetectionRadius && _detectionRadius > 0)
            {
                DrawDetectionRays();
            }

            UpdateBillboardRotation();
        }


        // PUBLIC METHODS

        /// <summary>
        /// Sets whether debugging is enabled
        /// </summary>
        /// <param name="enabled">True to enable debugging</param>
        public void SetDebugging(bool enabled)
        {
            _isDebugging = enabled;

            if (_debugBillboard != null)
            {
                _debugBillboard.gameObject.SetActive(enabled);
            }

            if (enabled)
            {
                FindLocalPlayerCamera();
            }
        }

        /// <summary>
        /// Updates the debug text display
        /// </summary>
        /// <param name="message">The message to display</param>
        public void UpdateDebugText(string message)
        {
            if (_debugText != null && _isDebugging)
            {
                _debugText.text = message;
            }
        }

        /// <summary>
        /// Sets the detection radius for visualization
        /// </summary>
        /// <param name="radius">The detection radius to visualize</param>
        public void SetDetectionRadius(float radius)
        {
            _detectionRadius = radius;
        }

        /// <summary>
        /// Sets whether to show detection radius visualization
        /// </summary>
        /// <param name="show">True to show detection rays</param>
        public void SetShowDetectionRadius(bool show)
        {
            _showDetectionRadius = show;
        }

        /// <summary>
        /// Sets the color for detection ray visualization
        /// </summary>
        /// <param name="color">The color to use for detection rays</param>
        public void SetDetectionRayColor(Color color)
        {
            _detectionRayColor = color;
        }

        /// <summary>
        /// Appends additional information to the current debug text
        /// </summary>
        /// <param name="additionalInfo">Additional information to append</param>
        public void AppendDebugInfo(string additionalInfo)
        {
            if (_debugText != null && _isDebugging)
            {
                _debugText.text += additionalInfo;
            }
        }


        // PRIVATE METHODS

        private void DrawDetectionRays()
        {
            if (_detectionRadius <= 0)
            {
                return;
            }

            float step = 360f / _detectionRayCount;

            for (int i = 0; i < _detectionRayCount; i++)
            {
                float angle = step * i * Mathf.Deg2Rad;
                Vector3 direction = new Vector3(Mathf.Cos(angle), 0f, Mathf.Sin(angle));

                UnityEngine.Debug.DrawRay(
                    transform.position,
                    direction * _detectionRadius,
                    _detectionRayColor
                );
            }
        }

        private void FindLocalPlayerCamera()
        {
            if (_localPlayerCamera != null || Time.time - _lastCameraSearchTime < CAMERA_SEARCH_INTERVAL)
            {
                return;
            }

            _lastCameraSearchTime = Time.time;

            // Try to find the local player's camera
            TRPPlayerController[] playerControllers = FindObjectsOfType<TRPPlayerController>();

            foreach (TRPPlayerController playerController in playerControllers)
            {
                if (playerController.IsMine)
                {
                    _localPlayerCamera = playerController.CameraRoot;
                    break;
                }
            }

            // Fallback to main camera if no local player camera found
            if (_localPlayerCamera == null && Camera.main != null)
            {
                _localPlayerCamera = Camera.main.transform;
            }
        }

        private void UpdateBillboardRotation()
        {
            if (_debugBillboard == null)
            {
                return;
            }

            if (_localPlayerCamera == null)
            {
                FindLocalPlayerCamera();
                return;
            }

            Vector3 directionToCamera = (_localPlayerCamera.position - _debugBillboard.transform.position).normalized;
            Quaternion targetRotation = Quaternion.LookRotation(directionToCamera);

            _debugBillboard.transform.rotation = Quaternion.Slerp(
                _debugBillboard.transform.rotation,
                targetRotation,
                Time.deltaTime * 5f
            );
        }


        // GIZMOS (EDITOR ONLY)

#if UNITY_EDITOR
        private void OnDrawGizmosSelected()
        {
            if (_detectionRadius > 0)
            {
                Gizmos.color = _detectionRayColor;
                Gizmos.DrawWireSphere(transform.position, _detectionRadius);
            }
        }
#endif
    }
}