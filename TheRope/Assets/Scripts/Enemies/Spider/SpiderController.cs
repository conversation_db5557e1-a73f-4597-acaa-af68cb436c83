// Copyright Isto Inc.

using Isto.Core.Beings;
using Isto.Core.StateMachine;
using Photon.Pun;
using RootMotion.Demos;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class SpiderController : BaseEnemyController, IPunObservable
    {
        public enum EnemyEnum
        {
            Idle,
            Patrol,
            Chase,
            Attack,
            Retreat,
            ScarePlayer,
            Dead
        }


        // UNITY HOOKUP

        [Header("-- SPIDER DEBUG --")]
        [SerializeField] private Canvas _debugBillboard;
        [SerializeField] private TextMeshProUGUI _debugEnemyStateText;
        [SerializeField] private bool _isDebugging = false;

        [Header("Spider Settings")]
        [SerializeField] private MechSpider _mechSpider;
        [SerializeField] private float _speed = 6f;
        [SerializeField] private float _turnSpeed = 60f;

        [Header("State Setup")]
        [SerializeField] private EnemyIdleState _idleState;
        [SerializeField] private EnemyPatrolState _patrolState;
        [SerializeField] private EnemyChaseState _chaseState;
        [SerializeField] private EnemyAttackState _attackState;
        [SerializeField] private EnemyRetreatState _retreatState;
        [SerializeField] private EnemyScarePlayerState _scarePlayerState;
        [SerializeField] private EnemyDeadState _deadState;

        [Header("Spider Variables")]
        [Tooltip("Which tag to look for when hunting players")]
        [SerializeField] private string _playerTag = "Player";
        [Tooltip("How far the spider can 'see'")]
        [SerializeField] private float _detectionRadius = 60;
        [SerializeField] private float _loseSightDistance = 85f;
        [SerializeField] private float _attackDistance = 15f;
        [SerializeField] private float _boredDuration = 10f;
        [SerializeField] private float _idleDuration = 5f;
        [SerializeField] private float _patrolDuration = 45f;


        // OTHER FIELDS

        private static readonly float CAMERA_SEARCH_INTERVAL = 1f;

        private Dictionary<EnemyEnum, MonoState> _stateMap;
        private List<Transform> _patrolPoints;
        private Transform _playerTarget;
        private Transform _patrolPointContainer;
        private bool _isAttacking = false;
        private Transform _localPlayerCamera;
        private float _lastCameraSearchTime;
        private PhotonView _photonView;
        private EnemyEnum _networkedCurrentState = EnemyEnum.Idle;
        private int _networkedTargetIndex = -1;
        private int _networkedPlayerTargetViewID = -1;
        private float _networkedStateTimer = 0f;
        private bool _isNetworkInitialized = false;


        // PROPERTIES

        public bool IsDebugging => _isDebugging;
        public float DistanceToTarget => CurrentTarget != null ? Vector3.Distance(transform.position, CurrentTarget.position) : 0f;
        public float LoseSightDistance => _loseSightDistance;
        public float AttackDistance => _attackDistance;
        public float BoredDuration => _boredDuration;
        public float IdleDuration => _idleDuration;
        public bool IsAttacking => _isAttacking;
        public float PatrolDuration => _patrolDuration;
        public List<Transform> PatrolPoints => _patrolPoints;
        public bool IsMasterClient => _photonView != null ? _photonView.IsMine && PhotonNetwork.IsMasterClient : true;
        public bool IsNetworked => _photonView != null && PhotonNetwork.IsConnected;

        // LIFECYCLE EVENTS

        protected void Awake()
        {
            _photonView = GetComponent<PhotonView>();

            _stateMap = new Dictionary<EnemyEnum, MonoState>
            {
                { EnemyEnum.Idle, _idleState },
                { EnemyEnum.Patrol, _patrolState },
                { EnemyEnum.Chase, _chaseState },
                { EnemyEnum.Attack, _attackState },
                { EnemyEnum.Retreat, _retreatState },
                { EnemyEnum.ScarePlayer, _scarePlayerState },
                { EnemyEnum.Dead, _deadState },
            };

            _patrolPointContainer = GameObject.Find("c_PatrolPoints").transform;
            _patrolPoints = new List<Transform>();
            foreach (Transform child in _patrolPointContainer)
            {
                _patrolPoints.Add(child);
            }

            _debugBillboard.gameObject.SetActive(_isDebugging);

            if (_isDebugging)
            {
                FindLocalPlayerCamera();
            }
        }

        protected override void Start()
        {
            base.Start();
            StartCoroutine(InitializeAfterNetworkReady());
        }

        protected override void Update()
        {
            if (IsDebugging)
            {
                DrawDetectionRays();
                var currentState = _currentState as EnemyState;
                string stateMessage = currentState?.DebugStateMessage ?? "No State";
                string networkInfo = IsNetworked ? $"\n{GetNetworkDebugInfo()}" : "\nSingle Player";
                _debugEnemyStateText.text = stateMessage + networkInfo;
            }

            UpdateStateMachine();

            if (CurrentTarget == null || _isAttacking)
            {
                return;
            }

            Vector3 dir = (CurrentTarget.position - transform.position).normalized;
            if (dir.sqrMagnitude > 0f)
            {
                Quaternion lookRot = Quaternion.LookRotation(dir);
                transform.rotation = Quaternion.RotateTowards(
                    transform.rotation,
                    lookRot,
                    Time.deltaTime * _turnSpeed
                );
                transform.Translate(
                    Vector3.forward * _speed * Time.deltaTime * _mechSpider.scale,
                    Space.Self
                );

                if (IsDebugging)
                {
                    UpdateBillboardRotation();
                }
            }
        }

        private IEnumerator InitializeAfterNetworkReady()
        {
            yield return new WaitForSeconds(0.1f);

            _isNetworkInitialized = true;

            if (IsNetworked)
            {
                if (IsMasterClient)
                {
                    Debug.Log("Spider Master Client: Starting in Idle state");
                    _networkedCurrentState = EnemyEnum.Idle;
                    base.ChangeState(_stateMap[EnemyEnum.Idle]);

                    yield return new WaitForSeconds(0.1f);
                    if (_photonView != null)
                    {
                        _photonView.RPC("OnNetworkStateChanged", RpcTarget.Others, (int)EnemyEnum.Idle, _networkedTargetIndex, _networkedPlayerTargetViewID, _networkedStateTimer);
                    }
                }
                else
                {
                    Debug.Log("Spider Client: Waiting for state sync from master");
                    StartCoroutine(WaitForNetworkInitialization());
                }
            }
            else
            {
                Debug.Log("Spider Single Player: Starting in Idle state");
                base.ChangeState(_stateMap[EnemyEnum.Idle]);
            }
        }

        private IEnumerator WaitForNetworkInitialization()
        {
            float waitTime = 0f;
            const float maxWaitTime = 2f;

            while (waitTime < maxWaitTime && _currentState == null)
            {
                yield return new WaitForSeconds(0.1f);
                waitTime += 0.1f;
            }

            if (_currentState == null)
            {
                Debug.Log("Spider Client: Timeout waiting for state sync, defaulting to Idle");
                base.ChangeState(_stateMap[EnemyEnum.Idle]);
            }
        }


        // ACCESSORS

        public MonoState GetState(EnemyEnum newState)
        {
            return _stateMap[newState];
        }

        public bool TryFindNearestPlayerWithinRadius()
        {
            var players = GameObject.FindGameObjectsWithTag(_playerTag);
            Transform best = null;
            float bestSqr = _detectionRadius * _detectionRadius;
            Vector3 myPos = transform.position;

            foreach (var go in players)
            {
                float d2 = (go.transform.position - myPos).sqrMagnitude;
                if (d2 <= bestSqr)
                {
                    bestSqr = d2;
                    best = go.transform;
                }
            }
            _playerTarget = best;
            if (best != null)
            {
                Debug.Log($"Spider: Found player within radius: {best.name}, Tag: {best.tag}");
            }
            return best != null;
        }

        public int GetRandomPatrolPointIndex(int excludeIndex = -1)
        {
            if (_patrolPoints == null || _patrolPoints.Count == 0)
                return -1;

            if (_patrolPoints.Count == 1)
                return 0;

            int seed = Mathf.FloorToInt(Time.time * 1000) + transform.position.GetHashCode();
            UnityEngine.Random.State oldState = UnityEngine.Random.state;
            UnityEngine.Random.InitState(seed);

            List<int> availableIndices = new List<int>();
            for (int i = 0; i < _patrolPoints.Count; i++)
            {
                if (i != excludeIndex)
                {
                    availableIndices.Add(i);
                }
            }

            int randomIndex = availableIndices.Count > 0 ?
                availableIndices[UnityEngine.Random.Range(0, availableIndices.Count)] : 0;

            UnityEngine.Random.state = oldState;
            return randomIndex;
        }

        // OTHER METHODS

        public void ChangeState(EnemyEnum newState)
        {
            if (IsNetworked && !IsMasterClient)
            {
                Debug.Log($"Spider (Client): Ignoring state change request to {newState} - not master client");
                return;
            }

            Debug.Log($"Spider (Master): Changing state to {newState} from {_currentState?.GetType().Name ?? "null"}");

            _networkedCurrentState = newState;
            base.ChangeState(_stateMap[newState]);

            if (IsNetworked && IsMasterClient)
            {
                Debug.Log($"Spider (Master): Broadcasting state change {newState} to clients");
                _photonView.RPC("OnNetworkStateChanged", RpcTarget.Others, (int)newState, _networkedTargetIndex, _networkedPlayerTargetViewID, _networkedStateTimer);
            }
        }

        public void FindNearestPlayer()
        {
            var players = GameObject.FindGameObjectsWithTag(_playerTag);
            Transform best = null;
            float bestSqr = float.MaxValue;
            Vector3 myPos = transform.position;

            foreach (var go in players)
            {
                float d2 = (go.transform.position - myPos).sqrMagnitude;
                if (d2 < bestSqr)
                {
                    bestSqr = d2;
                    best = go.transform;
                }
            }
            _playerTarget = best;
            SetTarget(best);
        }

        public new void SetTarget(Transform newTarget)
        {
            base.SetTarget(newTarget);

            if (newTarget != null)
            {
                if (_patrolPoints != null)
                {
                    _networkedTargetIndex = _patrolPoints.IndexOf(newTarget);
                }
                else
                {
                    _networkedTargetIndex = -1;
                }

                PhotonView targetPhotonView = newTarget.GetComponent<PhotonView>();
                if (targetPhotonView != null)
                {
                    _networkedPlayerTargetViewID = targetPhotonView.ViewID;
                    Debug.Log($"Spider: Set target to player {newTarget.name}, ViewID: {_networkedPlayerTargetViewID}, Tag: {newTarget.tag}");
                }
                else
                {
                    _networkedPlayerTargetViewID = -1;
                    Debug.Log($"Spider: Set target to {newTarget.name} (no PhotonView), patrol index: {_networkedTargetIndex}, Tag: {newTarget.tag}");
                }
            }
            else
            {
                _networkedTargetIndex = -1;
                _networkedPlayerTargetViewID = -1;
                Debug.Log("Spider: Set target to null");
            }
        }

        public void SetTargetByIndex(int patrolPointIndex)
        {
            if (patrolPointIndex >= 0 && patrolPointIndex < _patrolPoints.Count)
            {
                SetTarget(_patrolPoints[patrolPointIndex]);
            }
            else
            {
                SetTarget(null);
            }
        }

        public void SetTargetToPlayer()
        {
            if (_playerTarget != null)
            {
                SetTarget(_playerTarget);
            }
        }

        public void SetIsAttacking(bool isAttacking)
        {
            _isAttacking = isAttacking;
        }

        private void UpdateStateMachine()
        {
            if (_currentState == null) return;

            MonoState nextState = _currentState.Run(this);

            if (nextState != null && nextState != _currentState)
            {
                EnemyEnum? targetEnum = null;
                foreach (var kvp in _stateMap)
                {
                    if (kvp.Value == nextState)
                    {
                        targetEnum = kvp.Key;
                        break;
                    }
                }

                if (targetEnum.HasValue)
                {
                    ChangeState(targetEnum.Value);
                }
                else
                {
                    Debug.LogWarning("Spider: State transition bypassed networking - state not found in map");
                    base.ChangeState(nextState);
                }
            }
        }

        [PunRPC]
        private void OnNetworkStateChanged(int stateInt, int targetIndex, int playerTargetViewID, float stateTimer)
        {
            EnemyEnum newState = (EnemyEnum)stateInt;
            _networkedCurrentState = newState;
            _networkedTargetIndex = targetIndex;
            _networkedPlayerTargetViewID = playerTargetViewID;
            _networkedStateTimer = stateTimer;

            Transform targetToSet = null;

            if (playerTargetViewID > 0)
            {
                PhotonView playerPhotonView = PhotonView.Find(playerTargetViewID);
                if (playerPhotonView != null)
                {
                    targetToSet = playerPhotonView.transform;
                    Debug.Log($"Spider received state change: {newState}, player target: {targetToSet.name}");
                }
                else
                {
                    Debug.LogWarning($"Spider: Could not find player with ViewID {playerTargetViewID}");
                }
            }
            else if (targetIndex >= 0 && targetIndex < _patrolPoints.Count)
            {
                targetToSet = _patrolPoints[targetIndex];
                Debug.Log($"Spider received state change: {newState}, patrol target: {targetToSet.name}");
            }
            else
            {
                Debug.Log($"Spider received state change: {newState}, no target");
            }

            SetTarget(targetToSet);
            base.ChangeState(_stateMap[newState]);
        }

        private void DrawDetectionRays()
        {
            int rayCount = 36;
            float step = 360f / rayCount;

            for (int i = 0; i < rayCount; i++)
            {
                float angle = step * i * Mathf.Deg2Rad;
                Vector3 dir = new Vector3(Mathf.Cos(angle), 0f, Mathf.Sin(angle));
                Debug.DrawRay(
                    transform.position,
                    dir * _detectionRadius,
                    Color.red
                );
            }
        }

        private void FindLocalPlayerCamera()
        {
            if (_localPlayerCamera != null || Time.time - _lastCameraSearchTime < CAMERA_SEARCH_INTERVAL)
            {
                return;
            }

            _lastCameraSearchTime = Time.time;

            TRPPlayerController[] playerControllers = FindObjectsOfType<TRPPlayerController>();

            foreach (TRPPlayerController playerController in playerControllers)
            {
                if (playerController.IsMine)
                {
                    _localPlayerCamera = playerController.CameraRoot;
                    break;
                }
            }

            if (_localPlayerCamera == null && Camera.main != null)
            {
                _localPlayerCamera = Camera.main.transform;
            }
        }

        private void UpdateBillboardRotation()
        {
            if (_localPlayerCamera == null)
            {
                FindLocalPlayerCamera();
                return;
            }

            Vector3 directionToCamera = (_localPlayerCamera.position - _debugBillboard.transform.position).normalized;
            Quaternion targetRotation = Quaternion.LookRotation(directionToCamera);

            _debugBillboard.transform.rotation = Quaternion.Slerp(
                _debugBillboard.transform.rotation,
                targetRotation,
                Time.deltaTime * 5f
            );
        }

#if UNITY_EDITOR
        private void OnDrawGizmosSelected()
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, _detectionRadius);
        }
#endif


        // IPUNOBSERVABLE IMPLEMENTATION

        public void OnPhotonSerializeView(PhotonStream stream, PhotonMessageInfo info)
        {
            if (stream.IsWriting)
            {
                stream.SendNext((int)_networkedCurrentState);
                stream.SendNext(_networkedTargetIndex);
                stream.SendNext(_networkedPlayerTargetViewID);
                stream.SendNext(_networkedStateTimer);
            }
            else
            {
                int stateInt = (int)stream.ReceiveNext();
                int targetIndex = (int)stream.ReceiveNext();
                int playerTargetViewID = (int)stream.ReceiveNext();
                float stateTimer = (float)stream.ReceiveNext();

                if (!IsMasterClient && _isNetworkInitialized)
                {
                    EnemyEnum newState = (EnemyEnum)stateInt;

                    _networkedTargetIndex = targetIndex;
                    _networkedPlayerTargetViewID = playerTargetViewID;
                    _networkedStateTimer = stateTimer;

                    Transform targetToSet = null;

                    if (playerTargetViewID > 0)
                    {
                        PhotonView playerPhotonView = PhotonView.Find(playerTargetViewID);
                        if (playerPhotonView != null)
                        {
                            targetToSet = playerPhotonView.transform;
                        }
                    }
                    else if (targetIndex >= 0 && targetIndex < _patrolPoints.Count)
                    {
                        targetToSet = _patrolPoints[targetIndex];
                    }

                    if (CurrentTarget != targetToSet)
                    {
                        SetTarget(targetToSet);
                    }

                    if (_networkedCurrentState != newState)
                    {
                        _networkedCurrentState = newState;
                        Debug.Log($"Spider continuous sync: changing to {newState}");
                        base.ChangeState(_stateMap[newState]);
                    }
                }
            }
        }

        private string GetNetworkDebugInfo()
        {
            return $"IsMaster: {IsMasterClient}, IsNetworked: {IsNetworked}, State: {_networkedCurrentState}, Target: {_networkedTargetIndex}, PlayerTarget: {_networkedPlayerTargetViewID}";
        }
    }
}