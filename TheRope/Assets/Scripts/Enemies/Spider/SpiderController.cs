// Copyright Isto Inc.

using Isto.Core.Beings;
using Isto.Core.StateMachine;
using Isto.TRP.Enemies.Networking;
using Isto.TRP.Enemies.Services;
using Isto.TRP.Enemies.StateMachine;
using Photon.Pun;
using RootMotion.Demos;
using System.Collections.Generic;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class SpiderController : BaseEnemyController, INetworkedEnemyController
    {
        public enum EnemyEnum
        {
            Idle,
            Patrol,
            Chase,
            Attack,
            Retreat,
            ScarePlayer,
            Dead
        }


        // UNITY HOOKUP

        [Header("Spider Settings")]
        [SerializeField] private MechSpider _mechSpider;
        [SerializeField] private float _speed = 6f;
        [SerializeField] private float _turnSpeed = 60f;

        [Header("Components")]
        [SerializeField] private EnemyDebugVisualizer _debugVisualizer;
        [SerializeField] private EnemyNetworkSync _networkSync;

        [Header("State Setup")]
        [SerializeField] private EnemyIdleState _idleState;
        [SerializeField] private EnemyPatrolState _patrolState;
        [SerializeField] private EnemyChaseState _chaseState;
        [SerializeField] private EnemyAttackState _attackState;
        [SerializeField] private EnemyRetreatState _retreatState;
        [SerializeField] private EnemyScarePlayerState _scarePlayerState;
        [SerializeField] private EnemyDeadState _deadState;

        [Header("Spider Variables")]
        [Tooltip("Which tag to look for when hunting players")]
        [SerializeField] private string _playerTag = "Player";
        [Tooltip("How far the spider can 'see'")]
        [SerializeField] private float _detectionRadius = 60;
        [SerializeField] private float _loseSightDistance = 85f;
        [SerializeField] private float _attackDistance = 15f;
        [SerializeField] private float _boredDuration = 10f;
        [SerializeField] private float _idleDuration = 5f;
        [SerializeField] private float _patrolDuration = 45f;


        // OTHER FIELDS

        private Dictionary<EnemyEnum, MonoState> _stateMap;
        private List<Transform> _patrolPoints;
        private Transform _playerTarget;
        private Transform _patrolPointContainer;
        private bool _isAttacking = false;
        private EnemyEnum _currentStateEnum = EnemyEnum.Idle;


        // PROPERTIES

        public bool IsDebugging => _debugVisualizer != null && _debugVisualizer.IsDebugging;
        public float DistanceToTarget => CurrentTarget != null ? Vector3.Distance(transform.position, CurrentTarget.position) : 0f;
        public float LoseSightDistance => _loseSightDistance;
        public float AttackDistance => _attackDistance;
        public float BoredDuration => _boredDuration;
        public float IdleDuration => _idleDuration;
        public bool IsAttacking => _isAttacking;
        public float PatrolDuration => _patrolDuration;
        public List<Transform> PatrolPoints => _patrolPoints;
        public bool IsMasterClient => _networkSync != null ? _networkSync.IsMasterClient : true;
        public bool IsNetworked => _networkSync != null && _networkSync.IsNetworked;

        // LIFECYCLE EVENTS

        protected void Awake()
        {
            InitializeComponents();
            InitializeStates();
            InitializePatrolPoints();
        }

        private void InitializeComponents()
        {
            // Get or create debug visualizer
            if (_debugVisualizer == null)
            {
                _debugVisualizer = GetComponent<EnemyDebugVisualizer>();
            }

            // Get or create network sync
            if (_networkSync == null)
            {
                _networkSync = GetComponent<EnemyNetworkSync>();
            }

            // Setup debug visualizer
            if (_debugVisualizer != null)
            {
                _debugVisualizer.SetDetectionRadius(_detectionRadius);
            }
        }

        private void InitializeStates()
        {
            _stateMap = new Dictionary<EnemyEnum, MonoState>
            {
                { EnemyEnum.Idle, _idleState },
                { EnemyEnum.Patrol, _patrolState },
                { EnemyEnum.Chase, _chaseState },
                { EnemyEnum.Attack, _attackState },
                { EnemyEnum.Retreat, _retreatState },
                { EnemyEnum.ScarePlayer, _scarePlayerState },
                { EnemyEnum.Dead, _deadState },
            };
        }

        private void InitializePatrolPoints()
        {
            // This isn't the best, but it's a quick and dirty way to get the patrol points. Will need to fix this
            // later when we decide how we want to build the level
            _patrolPointContainer = GameObject.Find("c_PatrolPoints").transform;
            _patrolPoints = new List<Transform>();
            foreach (Transform child in _patrolPointContainer)
            {
                _patrolPoints.Add(child);
            }

            // Set patrol points in network sync for target resolution
            if (_networkSync != null)
            {
                _networkSync.SetPatrolPoints(_patrolPoints);
            }
        }

        protected override void Start()
        {
            base.Start();
            InitializeNetworking();
        }

        private void InitializeNetworking()
        {
            if (_networkSync != null)
            {
                _networkSync.OnNetworkInitialized += OnNetworkInitialized;
                _networkSync.OnNetworkStateReceived += OnNetworkStateReceived;
            }
            else
            {
                // Single player mode - start immediately
                OnNetworkInitialized();
            }
        }

        protected override void Update()
        {
            UpdateDebugVisualization();
            UpdateStateMachine();
            UpdateMovement();
        }

        private void UpdateDebugVisualization()
        {
            if (_debugVisualizer != null && IsDebugging)
            {
                var currentState = _currentState as BaseEnemyState<SpiderController>;
                string stateMessage = currentState?.DebugStateMessage ?? "No State";
                string networkInfo = IsNetworked ? $"\n{GetNetworkDebugInfo()}" : "\nSingle Player";
                _debugVisualizer.UpdateDebugText(stateMessage + networkInfo);
            }
        }

        private void UpdateMovement()
        {
            if (CurrentTarget == null || _isAttacking)
            {
                return;
            }

            Vector3 dir = (CurrentTarget.position - transform.position).normalized;
            if (dir.sqrMagnitude > 0f)
            {
                Quaternion lookRot = Quaternion.LookRotation(dir);
                transform.rotation = Quaternion.RotateTowards(
                    transform.rotation,
                    lookRot,
                    Time.deltaTime * _turnSpeed
                );
                transform.Translate(
                    Vector3.forward * _speed * Time.deltaTime * _mechSpider.scale,
                    Space.Self
                );
            }
        }

        private void OnNetworkInitialized()
        {
            if (IsNetworked)
            {
                if (IsMasterClient)
                {
                    Debug.Log("Spider Master Client: Starting in Idle state");
                    ChangeState(EnemyEnum.Idle);
                }
                else
                {
                    Debug.Log("Spider Client: Waiting for state sync from master");
                    // Client will receive state from master via network sync
                }
            }
            else
            {
                Debug.Log("Spider Single Player: Starting in Idle state");
                ChangeState(EnemyEnum.Idle);
            }
        }

        private void OnNetworkStateReceived(NetworkStateData stateData)
        {
            EnemyEnum newState = (EnemyEnum)stateData.StateId;
            _currentStateEnum = newState;

            // Resolve target from network data
            Transform targetToSet = null;
            if (_networkSync != null)
            {
                targetToSet = _networkSync.ResolveTarget(stateData.TargetIndex, stateData.PlayerTargetViewID);
            }

            Debug.Log($"Spider received state change: {newState}, target: {targetToSet?.name ?? "null"}");

            SetTarget(targetToSet);
            base.ChangeState(_stateMap[newState]);
        }


        // ACCESSORS

        public MonoState GetState(EnemyEnum newState)
        {
            return _stateMap[newState];
        }

        public bool TryFindNearestPlayerWithinRadius()
        {
            var result = PlayerDetectionService.FindNearestPlayerWithinRadius(transform.position, _playerTag, _detectionRadius);
            _playerTarget = result.Player;

            if (result.Found)
            {
                Debug.Log($"Spider: Found player within radius: {result.Player.name}, Distance: {result.Distance:F1}");
            }

            return result.Found;
        }

        public int GetRandomPatrolPointIndex(int excludeIndex = -1)
        {
            if (_patrolPoints == null || _patrolPoints.Count == 0)
                return -1;

            if (_patrolPoints.Count == 1)
                return 0;

            int seed = Mathf.FloorToInt(Time.time * 1000) + transform.position.GetHashCode();
            UnityEngine.Random.State oldState = UnityEngine.Random.state;
            UnityEngine.Random.InitState(seed);

            List<int> availableIndices = new List<int>();
            for (int i = 0; i < _patrolPoints.Count; i++)
            {
                if (i != excludeIndex)
                {
                    availableIndices.Add(i);
                }
            }

            int randomIndex = availableIndices.Count > 0 ?
                availableIndices[UnityEngine.Random.Range(0, availableIndices.Count)] : 0;

            UnityEngine.Random.state = oldState;
            return randomIndex;
        }

        // OTHER METHODS

        public void ChangeState(EnemyEnum newState)
        {
            if (IsNetworked && !IsMasterClient)
            {
                Debug.Log($"Spider (Client): Ignoring state change request to {newState} - not master client");
                return;
            }

            Debug.Log($"Spider (Master): Changing state to {newState} from {_currentState?.GetType().Name ?? "null"}");

            _currentStateEnum = newState;
            base.ChangeState(_stateMap[newState]);

            // Broadcast state change via network sync
            if (_networkSync != null && IsNetworked && IsMasterClient)
            {
                var stateData = _networkSync.CreateStateData((int)newState, CurrentTarget);
                _networkSync.BroadcastStateChange(stateData);
            }
        }

        public void FindNearestPlayer()
        {
            var result = PlayerDetectionService.FindNearestPlayer(transform.position, _playerTag);
            _playerTarget = result.Player;
            SetTarget(result.Player);

            if (result.Found)
            {
                Debug.Log($"Spider: Found nearest player: {result.Player.name}, Distance: {result.Distance:F1}");
            }
        }

        public new void SetTarget(Transform newTarget)
        {
            base.SetTarget(newTarget);

            if (newTarget != null)
            {
                PhotonView targetPhotonView = newTarget.GetComponent<PhotonView>();
                if (targetPhotonView != null)
                {
                    Debug.Log($"Spider: Set target to player {newTarget.name}, ViewID: {targetPhotonView.ViewID}, Tag: {newTarget.tag}");
                }
                else
                {
                    Debug.Log($"Spider: Set target to {newTarget.name} (no PhotonView), Tag: {newTarget.tag}");
                }
            }
            else
            {
                Debug.Log("Spider: Set target to null");
            }
        }

        public void SetTargetByIndex(int patrolPointIndex)
        {
            if (patrolPointIndex >= 0 && patrolPointIndex < _patrolPoints.Count)
            {
                SetTarget(_patrolPoints[patrolPointIndex]);
            }
            else
            {
                SetTarget(null);
            }
        }

        public void SetTargetToPlayer()
        {
            if (_playerTarget != null)
            {
                SetTarget(_playerTarget);
            }
        }

        public void SetIsAttacking(bool isAttacking)
        {
            _isAttacking = isAttacking;
        }

        private void UpdateStateMachine()
        {
            if (_currentState == null) return;

            MonoState nextState = _currentState.Run(this);

            if (nextState != null && nextState != _currentState)
            {
                EnemyEnum? targetEnum = null;
                foreach (var kvp in _stateMap)
                {
                    if (kvp.Value == nextState)
                    {
                        targetEnum = kvp.Key;
                        break;
                    }
                }

                if (targetEnum.HasValue)
                {
                    ChangeState(targetEnum.Value);
                }
                else
                {
                    Debug.LogWarning("Spider: State transition bypassed networking - state not found in map");
                    base.ChangeState(nextState);
                }
            }
        }

        private string GetNetworkDebugInfo()
        {
            return $"IsMaster: {IsMasterClient}, IsNetworked: {IsNetworked}, State: {_currentStateEnum}";
        }

    }
}