// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyScarePlayerState : EnemyState
    {
        // OTHER FIELDS

        private float _timer;

        protected override void OnEnterState()
        {
            _timer = 0f;
            SetDebugMessage("Spider is Scaring Player...");
        }

        protected override MonoState OnRunNetworkedState()
        {
            _timer += Time.deltaTime;

            // Simple scare logic - go back to chase after a short time
            if (_timer >= 2f)
            {
                return _controller.GetState(SpiderController.EnemyEnum.Chase);
            }

            return this;
        }

        protected override void OnExitState()
        {
            // cleanup if needed
        }
    }
}