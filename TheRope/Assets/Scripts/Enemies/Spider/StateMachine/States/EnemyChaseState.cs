// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyChaseState : EnemyState
    {
        private float _timer;
        private bool _isGettingBored;

        protected override void OnEnterState()
        {
            _timer = 0f;
            _isGettingBored = false;

            // Only master client finds new targets
            if (ShouldProcessLogic())
            {
                _controller.FindNearestPlayer();
            }

            UpdateDebugMessage();
        }

        protected override MonoState OnRunNetworkedState()
        {
            // Check if we still have a target
            if (_controller.CurrentTarget == null)
            {
                // Lost target, go back to patrol
                return _controller.GetState(SpiderController.EnemyEnum.Patrol);
            }

            // Check if close enough to attack
            if (_controller.DistanceToTarget <= _controller.AttackDistance)
            {
                return _controller.GetState(SpiderController.EnemyEnum.Attack);
            }

            // Check if target is too far away (lost sight)
            if (_controller.DistanceToTarget >= _controller.LoseSightDistance)
            {
                _isGettingBored = true;
            }

            if (_isGettingBored)
            {
                _timer += Time.deltaTime;
                if (_timer >= _controller.BoredDuration)
                {
                    // Give up chase, go back to patrol
                    _controller.SetTarget(null);
                    return _controller.GetState(SpiderController.EnemyEnum.Patrol);
                }
            }

            UpdateDebugMessage();
            return this;
        }

        protected override void OnUpdateDebugInfo()
        {
            UpdateDebugMessage();
        }

        protected override void OnExitState()
        {
            // cleanup if needed
        }

        private void UpdateDebugMessage()
        {
            if (_controller.CurrentTarget != null)
            {
                SetDebugMessage($"Spider is Chasing target: {_controller.CurrentTarget.name} (Distance: {_controller.DistanceToTarget:F1})");
            }
            else
            {
                SetDebugMessage("Spider is Chasing but has no target.");
            }
        }
    }
}