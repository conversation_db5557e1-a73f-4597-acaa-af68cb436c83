// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyAttackState : EnemyState
    {
        // OTHER FIELDS

        private static readonly float ATTACK_DURATION = 1.5f;

        private float _attackTimer;

        protected override void OnEnterState()
        {
            _attackTimer = 0f;
            _controller.SetIsAttacking(true);

            if (_controller.CurrentTarget != null)
            {
                SetDebugMessage($"Spider is Attacking {_controller.CurrentTarget.name}");
            }
            else
            {
                SetDebugMessage("Spider is Attacking but has no target");
            }
        }

        protected override MonoState OnRunNetworkedState()
        {
            if (!_controller.IsAttacking)
            {
                return this;
            }

            // Check if target is still within attack range during the attack
            if (_controller.DistanceToTarget > _controller.AttackDistance)
            {
                if (_controller.IsDebugging)
                {
                    Debug.Log("Target moved out of range during attack, returning to chase");
                }
                return _controller.GetState(SpiderController.EnemyEnum.Chase);
            }

            // Update attack timer
            _attackTimer += Time.deltaTime;

            // Check if attack duration has completed
            if (_attackTimer >= ATTACK_DURATION)
            {
                if (_controller.IsDebugging)
                {
                    Debug.Log("Attack completed, returning to chase");
                }
                return _controller.GetState(SpiderController.EnemyEnum.Chase);
            }

            // Continue attacking
            return this;
        }

        protected override void OnExitState()
        {
            _controller.SetIsAttacking(false);

            if (_controller.IsDebugging)
            {
                Debug.Log("Spider finished attacking");
            }
        }
    }
}