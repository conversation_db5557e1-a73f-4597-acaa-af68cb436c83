// Copyright Isto Inc.

using Isto.TRP.Enemies.StateMachine;

namespace Isto.TRP.Enemies
{
    /// <summary>
    /// Base class for Spider-specific enemy states.
    /// Uses the generic NetworkedEnemyState to provide networking functionality.
    /// </summary>
    public abstract class EnemyState : NetworkedEnemyState<SpiderController>
    {
        // This class now inherits all functionality from NetworkedEnemyState<SpiderController>
        // Individual state classes should override OnEnterState, OnExitState, and OnRunNetworkedState
    }
}