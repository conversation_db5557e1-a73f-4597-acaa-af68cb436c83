// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyPatrolState : EnemyState
    {
        // OTHER FIELDS

        private float _timer;

        protected override void OnEnterState()
        {
            _timer = 0f;

            // Only master client selects new patrol targets
            if (ShouldProcessLogic())
            {
                SetRandomPatrolTarget();
            }

            UpdateDebugMessage();
        }

        protected override MonoState OnRunNetworkedState()
        {
            _timer += Time.deltaTime;

            // Example transition: after patrolDuration, go to idle
            if (_timer >= _controller.PatrolDuration)
            {
                return _controller.GetState(SpiderController.EnemyEnum.Idle);
            }

            // Check for players within detection radius
            if (_controller.TryFindNearestPlayerWithinRadius())
            {
                _controller.SetTargetToPlayer(); // Switch target to the detected player
                return _controller.GetState(SpiderController.EnemyEnum.Chase);
            }

            // Check if we need a new patrol target
            if (_controller.CurrentTarget == null || _controller.DistanceToTarget <= 10f)
            {
                FindNewPatrolTarget();
            }

            UpdateDebugMessage();
            return this;
        }

        protected override void OnUpdateDebugInfo()
        {
            UpdateDebugMessage();
        }

        protected override void OnExitState()
        {
            // cleanup if needed
        }

        private void FindNewPatrolTarget()
        {
            var patrolPoints = _controller.PatrolPoints;
            if (patrolPoints == null || patrolPoints.Count == 0)
                return;

            if (patrolPoints.Count == 1)
            {
                // If there's only one patrol point, just stay there
                _controller.SetTargetByIndex(0);
            }
            else
            {
                // Get current target index to exclude it
                int currentIndex = _controller.CurrentTarget != null ?
                    patrolPoints.IndexOf(_controller.CurrentTarget) : -1;

                // Use deterministic random selection for network consistency
                int newIndex = _controller.GetRandomPatrolPointIndex(currentIndex);
                _controller.SetTargetByIndex(newIndex);
            }
        }

        private void SetRandomPatrolTarget()
        {
            if (_controller.PatrolPoints != null && _controller.PatrolPoints.Count > 0)
            {
                // Use deterministic random selection for network consistency
                int randomIndex = _controller.GetRandomPatrolPointIndex();
                _controller.SetTargetByIndex(randomIndex);
            }
        }

        private void UpdateDebugMessage()
        {
            if (_controller.CurrentTarget != null)
            {
                SetDebugMessage("Spider is Patrolling. Moving to target: " + _controller.CurrentTarget.name);
            }
            else
            {
                SetDebugMessage("Spider is Patrolling. No target set.");
            }
        }
    }
}