// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyIdleState : EnemyState
    {
        // OTHER FIELDS

        private float _timer;

        protected override void OnEnterState()
        {
            _timer = 0f;
            SetDebugMessage("Spider is Idling...");
        }

        protected override MonoState OnRunNetworkedState()
        {
            _timer += Time.deltaTime;

            // Check for players within detection radius first
            if (_controller.TryFindNearestPlayerWithinRadius())
            {
                _controller.SetTargetToPlayer(); // Switch target to the detected player
                return _controller.GetState(SpiderController.EnemyEnum.Chase);
            }

            // After idle duration, start patrolling
            if (_timer >= _controller.IdleDuration)
            {
                return _controller.GetState(SpiderController.EnemyEnum.Patrol);
            }

            return this;
        }

        protected override void OnExitState()
        {
            // cleanup if needed
        }
    }
}