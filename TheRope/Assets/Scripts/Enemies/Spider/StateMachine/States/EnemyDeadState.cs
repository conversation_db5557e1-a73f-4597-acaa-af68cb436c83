// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.TRP.Enemies
{
    public class EnemyDeadState : EnemyState
    {
        protected override void OnEnterState()
        {
            SetDebugMessage("Spider is Dead");

            // Stop all movement and attacks
            _controller.SetIsAttacking(false);
            _controller.SetTarget(null);
        }

        protected override MonoState OnRunNetworkedState()
        {
            // Dead state - no transitions, stays dead
            return this;
        }

        protected override void OnExitState()
        {
            // cleanup if needed - respawn logic would go here
        }
    }
}