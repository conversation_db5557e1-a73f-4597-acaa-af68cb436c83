// Copyright Isto Inc.

using Isto.Core.Beings;
using Isto.Core.StateMachine;

namespace Isto.TRP.Enemies.StateMachine
{
    /// <summary>
    /// Generic base class for enemy states that provides common functionality
    /// while remaining reusable across different enemy controller types.
    /// </summary>
    /// <typeparam name="T">The type of enemy controller this state works with</typeparam>
    public abstract class BaseEnemyState<T> : MonoState where T : BaseEnemyController
    {
        // PROTECTED FIELDS

        protected T _controller;
        protected string _debugStateMessage = "";

        
        // PROPERTIES

        /// <summary>
        /// Debug message for this state, used for debugging and visualization
        /// </summary>
        public string DebugStateMessage => _debugStateMessage;

        /// <summary>
        /// The enemy controller this state is operating on
        /// </summary>
        protected T Controller => _controller;


        // LIFECYCLE METHODS

        public override void Enter(MonoStateMachine controller)
        {
            _controller = controller as T;
            if (_controller == null)
            {
                UnityEngine.Debug.LogError($"State {GetType().Name} expected controller of type {typeof(T).Name} but got {controller?.GetType().Name ?? "null"}");
                return;
            }

            OnEnterState();
        }

        public override void Exit(MonoStateMachine controller)
        {
            OnExitState();
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_controller == null)
            {
                return this;
            }

            return OnRunState();
        }


        // ABSTRACT METHODS FOR DERIVED CLASSES

        /// <summary>
        /// Called when entering this state. Override to implement state-specific enter logic.
        /// </summary>
        protected abstract void OnEnterState();

        /// <summary>
        /// Called when exiting this state. Override to implement state-specific exit logic.
        /// </summary>
        protected abstract void OnExitState();

        /// <summary>
        /// Called every frame while in this state. Override to implement state-specific logic.
        /// Should return the next state to transition to, or this state to remain in current state.
        /// </summary>
        /// <returns>The next state to transition to, or this state to stay in current state</returns>
        protected abstract MonoState OnRunState();


        // UTILITY METHODS

        /// <summary>
        /// Updates the debug message for this state
        /// </summary>
        /// <param name="message">The debug message to set</param>
        protected void SetDebugMessage(string message)
        {
            _debugStateMessage = message;
        }

        /// <summary>
        /// Appends text to the current debug message
        /// </summary>
        /// <param name="additionalText">Text to append</param>
        protected void AppendDebugMessage(string additionalText)
        {
            _debugStateMessage += additionalText;
        }
    }

    /// <summary>
    /// Specialized base class for networked enemy states that includes common networking logic.
    /// </summary>
    /// <typeparam name="T">The type of networked enemy controller this state works with</typeparam>
    public abstract class NetworkedEnemyState<T> : BaseEnemyState<T> where T : BaseEnemyController, INetworkedEnemyController
    {
        /// <summary>
        /// Checks if this client should process game logic (i.e., is the master client in networked mode)
        /// </summary>
        /// <returns>True if logic should be processed, false if this is a non-master client</returns>
        protected bool ShouldProcessLogic()
        {
            return !_controller.IsNetworked || _controller.IsMasterClient;
        }

        /// <summary>
        /// Template method that handles network authority checking before running state logic.
        /// Non-master clients will skip logic processing but can still update debug messages.
        /// </summary>
        protected override MonoState OnRunState()
        {
            if (!ShouldProcessLogic())
            {
                // Non-master clients can still update debug info but don't process game logic
                OnUpdateDebugInfo();
                return this;
            }

            return OnRunNetworkedState();
        }

        /// <summary>
        /// Called every frame for master clients to process game logic.
        /// Override this instead of OnRunState for networked states.
        /// </summary>
        /// <returns>The next state to transition to, or this state to stay in current state</returns>
        protected abstract MonoState OnRunNetworkedState();

        /// <summary>
        /// Called every frame for all clients to update debug information.
        /// Override to provide debug info updates that should happen on all clients.
        /// </summary>
        protected virtual void OnUpdateDebugInfo()
        {
            // Default implementation does nothing
        }
    }

    /// <summary>
    /// Interface for networked enemy controllers to provide networking information
    /// </summary>
    public interface INetworkedEnemyController
    {
        /// <summary>
        /// True if this controller is running in a networked environment
        /// </summary>
        bool IsNetworked { get; }

        /// <summary>
        /// True if this client is the master client (has authority to make decisions)
        /// </summary>
        bool IsMasterClient { get; }
    }
}
