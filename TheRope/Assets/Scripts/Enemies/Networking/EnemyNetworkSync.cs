// Copyright Isto Inc.

using System;
using System.Collections;
using System.Collections.Generic;
using Photon.Pun;
using UnityEngine;

namespace Isto.TRP.Enemies.Networking
{
    /// <summary>
    /// Component responsible for synchronizing enemy state and targets across the network.
    /// Handles all networking logic for enemy controllers.
    /// </summary>
    public class EnemyNetworkSync : MonoBehaviourPun, IPunObservable
    {
        // EVENTS

        /// <summary>
        /// Event fired when a network state change is received
        /// </summary>
        public event Action<NetworkStateData> OnNetworkStateReceived;

        /// <summary>
        /// Event fired when network initialization is complete
        /// </summary>
        public event Action OnNetworkInitialized;


        // PRIVATE FIELDS

        private bool _isNetworkInitialized = false;
        private NetworkStateData _currentNetworkState;
        private List<Transform> _patrolPoints;


        // PROPERTIES

        /// <summary>
        /// True if this is running in a networked environment
        /// </summary>
        public bool IsNetworked => photonView != null && PhotonNetwork.IsConnected;

        /// <summary>
        /// True if this client is the master client
        /// </summary>
        public bool IsMasterClient => photonView != null ? photonView.IsMine && PhotonNetwork.IsMasterClient : true;

        /// <summary>
        /// True if network initialization is complete
        /// </summary>
        public bool IsNetworkInitialized => _isNetworkInitialized;


        // LIFECYCLE METHODS

        private void Start()
        {
            StartCoroutine(InitializeNetworking());
        }


        // PUBLIC METHODS

        /// <summary>
        /// Sets the patrol points for target resolution
        /// </summary>
        /// <param name="patrolPoints">List of patrol point transforms</param>
        public void SetPatrolPoints(List<Transform> patrolPoints)
        {
            _patrolPoints = patrolPoints;
        }

        /// <summary>
        /// Broadcasts a state change to other clients (master client only)
        /// </summary>
        /// <param name="stateData">The state data to broadcast</param>
        public void BroadcastStateChange(NetworkStateData stateData)
        {
            if (!IsNetworked || !IsMasterClient)
            {
                return;
            }

            _currentNetworkState = stateData;

            Debug.Log($"Enemy Network: Broadcasting state change {stateData.StateId} to clients");
            photonView.RPC("OnNetworkStateChanged", RpcTarget.Others,
                stateData.StateId, stateData.TargetIndex, stateData.PlayerTargetViewID, stateData.StateTimer);
        }

        /// <summary>
        /// Resolves a target transform from network data
        /// </summary>
        /// <param name="targetIndex">Patrol point index (-1 if not a patrol point)</param>
        /// <param name="playerTargetViewID">Player PhotonView ID (-1 if not a player)</param>
        /// <returns>The resolved target transform, or null if not found</returns>
        public Transform ResolveTarget(int targetIndex, int playerTargetViewID)
        {
            if (playerTargetViewID > 0)
            {
                PhotonView playerPhotonView = PhotonView.Find(playerTargetViewID);
                if (playerPhotonView != null)
                {
                    return playerPhotonView.transform;
                }
                else
                {
                    Debug.LogWarning($"Enemy Network: Could not find player with ViewID {playerTargetViewID}");
                }
            }
            else if (targetIndex >= 0 && _patrolPoints != null && targetIndex < _patrolPoints.Count)
            {
                return _patrolPoints[targetIndex];
            }

            return null;
        }

        /// <summary>
        /// Creates network state data from current target information
        /// </summary>
        /// <param name="stateId">The current state ID</param>
        /// <param name="currentTarget">The current target transform</param>
        /// <param name="stateTimer">Optional state timer value</param>
        /// <returns>NetworkStateData structure</returns>
        public NetworkStateData CreateStateData(int stateId, Transform currentTarget, float stateTimer = 0f)
        {
            int targetIndex = -1;
            int playerTargetViewID = -1;

            if (currentTarget != null)
            {
                // Check if it's a patrol point
                if (_patrolPoints != null)
                {
                    targetIndex = _patrolPoints.IndexOf(currentTarget);
                }

                // Check if it's a player
                PhotonView targetPhotonView = currentTarget.GetComponent<PhotonView>();
                if (targetPhotonView != null)
                {
                    playerTargetViewID = targetPhotonView.ViewID;
                }
            }

            return new NetworkStateData
            {
                StateId = stateId,
                TargetIndex = targetIndex,
                PlayerTargetViewID = playerTargetViewID,
                StateTimer = stateTimer
            };
        }


        // PRIVATE METHODS

        private IEnumerator InitializeNetworking()
        {
            yield return new WaitForSeconds(0.1f);
            _isNetworkInitialized = true;
            OnNetworkInitialized?.Invoke();
        }

        private IEnumerator WaitForNetworkInitialization()
        {
            float waitTime = 0f;
            const float maxWaitTime = 2f;

            while (waitTime < maxWaitTime && !_isNetworkInitialized)
            {
                yield return new WaitForSeconds(0.1f);
                waitTime += 0.1f;
            }

            if (!_isNetworkInitialized)
            {
                Debug.Log("Enemy Network: Timeout waiting for network initialization");
                _isNetworkInitialized = true;
                OnNetworkInitialized?.Invoke();
            }
        }


        // PHOTON RPC METHODS

        [PunRPC]
        private void OnNetworkStateChanged(int stateId, int targetIndex, int playerTargetViewID, float stateTimer)
        {
            var stateData = new NetworkStateData
            {
                StateId = stateId,
                TargetIndex = targetIndex,
                PlayerTargetViewID = playerTargetViewID,
                StateTimer = stateTimer
            };

            _currentNetworkState = stateData;

            Debug.Log($"Enemy Network: Received state change {stateId}, target index: {targetIndex}, player ViewID: {playerTargetViewID}");
            OnNetworkStateReceived?.Invoke(stateData);
        }


        // IPUNOBSERVABLE IMPLEMENTATION

        public void OnPhotonSerializeView(PhotonStream stream, PhotonMessageInfo info)
        {
            if (stream.IsWriting)
            {
                // Send current state data
                stream.SendNext(_currentNetworkState.StateId);
                stream.SendNext(_currentNetworkState.TargetIndex);
                stream.SendNext(_currentNetworkState.PlayerTargetViewID);
                stream.SendNext(_currentNetworkState.StateTimer);
            }
            else
            {
                // Receive state data
                int stateId = (int)stream.ReceiveNext();
                int targetIndex = (int)stream.ReceiveNext();
                int playerTargetViewID = (int)stream.ReceiveNext();
                float stateTimer = (float)stream.ReceiveNext();

                if (!IsMasterClient && _isNetworkInitialized)
                {
                    var newStateData = new NetworkStateData
                    {
                        StateId = stateId,
                        TargetIndex = targetIndex,
                        PlayerTargetViewID = playerTargetViewID,
                        StateTimer = stateTimer
                    };

                    // Only fire event if state actually changed
                    if (!_currentNetworkState.Equals(newStateData))
                    {
                        _currentNetworkState = newStateData;
                        Debug.Log($"Enemy Network: Continuous sync - state {stateId}");
                        OnNetworkStateReceived?.Invoke(newStateData);
                    }
                }
            }
        }
    }

    /// <summary>
    /// Data structure for network state synchronization
    /// </summary>
    [System.Serializable]
    public struct NetworkStateData : IEquatable<NetworkStateData>
    {
        public int StateId;
        public int TargetIndex;
        public int PlayerTargetViewID;
        public float StateTimer;

        public bool Equals(NetworkStateData other)
        {
            return StateId == other.StateId &&
                   TargetIndex == other.TargetIndex &&
                   PlayerTargetViewID == other.PlayerTargetViewID &&
                   Mathf.Approximately(StateTimer, other.StateTimer);
        }

        public override bool Equals(object obj)
        {
            return obj is NetworkStateData other && Equals(other);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(StateId, TargetIndex, PlayerTargetViewID, StateTimer);
        }
    }
}