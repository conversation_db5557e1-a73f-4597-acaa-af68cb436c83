// Copyright Isto Inc.

using System.Collections.Generic;
using UnityEngine;

namespace Isto.TRP.Enemies.Services
{
    /// <summary>
    /// Service for detecting and finding players in the game world.
    /// Consolidates player detection logic that can be reused across different enemy types.
    /// </summary>
    public static class PlayerDetectionService
    {
        /// <summary>
        /// Finds the nearest player to a given position within an optional maximum distance.
        /// </summary>
        /// <param name="position">The position to search from</param>
        /// <param name="playerTag">The tag to search for (usually "Player")</param>
        /// <param name="maxDistance">Optional maximum distance to search within</param>
        /// <returns>PlayerDetectionResult containing the found player and distance information</returns>
        public static PlayerDetectionResult FindNearestPlayer(Vector3 position, string playerTag, float? maxDistance = null)
        {
            var players = GameObject.FindGameObjectsWithTag(playerTag);
            Transform bestPlayer = null;
            float bestDistanceSqr = maxDistance.HasValue ? maxDistance.Value * maxDistance.Value : float.MaxValue;

            foreach (var playerGameObject in players)
            {
                float distanceSqr = (playerGameObject.transform.position - position).sqrMagnitude;
                if (distanceSqr < bestDistanceSqr)
                {
                    bestDistanceSqr = distanceSqr;
                    bestPlayer = playerGameObject.transform;
                }
            }

            return new PlayerDetectionResult
            {
                Player = bestPlayer,
                Distance = bestPlayer != null ? Mathf.Sqrt(bestDistanceSqr) : 0f,
                DistanceSquared = bestDistanceSqr
            };
        }

        /// <summary>
        /// Finds the nearest player within a specific detection radius.
        /// </summary>
        /// <param name="position">The position to search from</param>
        /// <param name="playerTag">The tag to search for (usually "Player")</param>
        /// <param name="detectionRadius">The maximum detection radius</param>
        /// <returns>PlayerDetectionResult containing the found player and distance information</returns>
        public static PlayerDetectionResult FindNearestPlayerWithinRadius(Vector3 position, string playerTag, float detectionRadius)
        {
            return FindNearestPlayer(position, playerTag, detectionRadius);
        }

        /// <summary>
        /// Gets all players within a specified radius of a position.
        /// </summary>
        /// <param name="position">The position to search from</param>
        /// <param name="playerTag">The tag to search for (usually "Player")</param>
        /// <param name="radius">The search radius</param>
        /// <returns>List of PlayerDetectionResult for all players within radius</returns>
        public static List<PlayerDetectionResult> FindAllPlayersWithinRadius(Vector3 position, string playerTag, float radius)
        {
            var results = new List<PlayerDetectionResult>();
            var players = GameObject.FindGameObjectsWithTag(playerTag);
            float radiusSqr = radius * radius;

            foreach (var playerGameObject in players)
            {
                float distanceSqr = (playerGameObject.transform.position - position).sqrMagnitude;
                if (distanceSqr <= radiusSqr)
                {
                    results.Add(new PlayerDetectionResult
                    {
                        Player = playerGameObject.transform,
                        Distance = Mathf.Sqrt(distanceSqr),
                        DistanceSquared = distanceSqr
                    });
                }
            }

            return results;
        }

        /// <summary>
        /// Checks if any player is within the specified radius of a position.
        /// </summary>
        /// <param name="position">The position to search from</param>
        /// <param name="playerTag">The tag to search for (usually "Player")</param>
        /// <param name="radius">The search radius</param>
        /// <returns>True if at least one player is within radius</returns>
        public static bool IsPlayerWithinRadius(Vector3 position, string playerTag, float radius)
        {
            var players = GameObject.FindGameObjectsWithTag(playerTag);
            float radiusSqr = radius * radius;

            foreach (var playerGameObject in players)
            {
                float distanceSqr = (playerGameObject.transform.position - position).sqrMagnitude;
                if (distanceSqr <= radiusSqr)
                {
                    return true;
                }
            }

            return false;
        }
    }

    /// <summary>
    /// Result structure for player detection operations.
    /// Contains information about a detected player and their distance.
    /// </summary>
    public struct PlayerDetectionResult
    {
        /// <summary>
        /// The transform of the detected player (null if no player found)
        /// </summary>
        public Transform Player { get; set; }

        /// <summary>
        /// The distance to the detected player (0 if no player found)
        /// </summary>
        public float Distance { get; set; }

        /// <summary>
        /// The squared distance to the detected player (useful for performance comparisons)
        /// </summary>
        public float DistanceSquared { get; set; }

        /// <summary>
        /// True if a player was found, false otherwise
        /// </summary>
        public bool Found => Player != null;

        /// <summary>
        /// Returns a string representation of the detection result
        /// </summary>
        public override string ToString()
        {
            return Found ? $"Player: {Player.name}, Distance: {Distance:F2}" : "No player found";
        }
    }
}
